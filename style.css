* {
 margin: 0;
 padding: 0;
 box-sizing: border-box;
 font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
 background-color: #f5f7fa;
 height: 100vh;
 display: flex;
 align-items: center;
 justify-content: center;
}

.container {
 display: flex;
 width: 90%;
 max-width: 1200px;
 min-height: 600px;
 background-color: #fff;
 border-radius: 10px;
 box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
 overflow: hidden;
}

/* Login Box (sebelah kiri) */
.login-box {
 width: 30%;
 padding: 40px;
 background-color: #fff;
 display: flex;
 flex-direction: column;
 justify-content: center;
}

.login-box h2 {
 margin-bottom: 30px;
 color: #333;
 font-size: 24px;
 text-align: center;
}

.form-group {
 margin-bottom: 20px;
}

.form-group label {
 display: block;
 margin-bottom: 8px;
 font-size: 14px;
 color: #555;
}

.form-group input {
 width: 100%;
 padding: 12px;
 border: 1px solid #ddd;
 border-radius: 5px;
 font-size: 14px;
 transition: border-color 0.3s;
}

.form-group input:focus {
 outline: none;
 border-color: #4a90e2;
}

.form-group button {
 width: 100%;
 padding: 12px;
 background-color: #4a90e2;
 color: white;
 border: none;
 border-radius: 5px;
 cursor: pointer;
 font-size: 16px;
 font-weight: 500;
 transition: background-color 0.3s;
}

.form-group button:hover {
 background-color: #3a7bc8;
}

.form-links {
 display: flex;
 justify-content: space-between;
 margin-top: 15px;
}

.form-links a {
 color: #4a90e2;
 text-decoration: none;
 font-size: 13px;
}

.form-links a:hover {
 text-decoration: underline;
}

/* Info Box (sebelah kanan) */
.info-box {
 width: 70%;
 padding: 50px;
 background-color: #4a90e2;
 color: white;
}

.info-box h1 {
 font-size: 32px;
 margin-bottom: 30px;
}

.info-content {
 max-width: 80%;
}

.info-content > p {
 margin-bottom: 30px;
 line-height: 1.6;
}

.info-section {
 margin-bottom: 25px;
}

.info-section h3 {
 margin-bottom: 15px;
 font-size: 20px;
 font-weight: 500;
}

.info-section ul {
 list-style-type: none;
}

.info-section ul li {
 margin-bottom: 10px;
 padding-left: 20px;
 position: relative;
}

.info-section ul li:before {
 content: "✓";
 position: absolute;
 left: 0;
 color: #fff;
}

.cta-button {
 margin-top: 30px;
}

.cta-button a {
 display: inline-block;
 padding: 12px 25px;
 background-color: white;
 color: #4a90e2;
 text-decoration: none;
 border-radius: 5px;
 font-weight: 500;
 transition: all 0.3s;
}

.cta-button a:hover {
 background-color: rgba(255, 255, 255, 0.9);
 transform: translateY(-2px);
 box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
 .container {
 flex-direction: column;
 width: 95%;
 }
 
 .login-box, .info-box {
 width: 100%;
 }
 
 .login-box {
 order: 2;
 padding: 30px 20px;
 }
 
 .info-box {
 order: 1;
 padding: 30px 20px;
 }
 
 .info-content {
 max-width: 100%;
 }
}