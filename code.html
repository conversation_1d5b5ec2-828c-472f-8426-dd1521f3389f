<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>WhatsApp Bulk Message Sender</title>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap');

  body {
    margin: 0;
    padding: 0;
    font-family: 'Montserrat', sans-serif;
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: #fff;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .container {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem 3rem;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    max-width: 420px;
    width: 100%;
  }

  h1 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 1px 1px 5px rgba(0,0,0,0.3);
  }

  label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  textarea, input[type="text"] {
    width: 100%;
    border-radius: 8px;
    border: none;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    resize: vertical;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
  }

  textarea {
    min-height: 100px;
  }

  .note {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: -1rem;
    margin-bottom: 1.5rem;
  }

  button {
    width: 100%;
    background: #075E54;
    border: none;
    padding: 0.85rem;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: background-color 0.3s ease;
  }

  button:hover {
    background: #128C7E;
  }

  .footer {
    text-align: center;
    margin-top: 1rem;
    font-size: 0.85rem;
    opacity: 0.7;
  }

  .error {
    background: #ff4d4f;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 700;
    text-align: center;
  }

  .success {
    background: #4caf50;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 700;
    text-align: center;
  }
</style>
</head>
<body>
  <div class="container" role="main" aria-label="WhatsApp Bulk Message Sender App">
    <h1>WhatsApp Bulk Sender</h1>

    <label for="contacts">Phone Numbers</label>
    <textarea id="contacts" placeholder="Enter phone numbers here, one per line&#10;Use international format without '+' or spaces (e.g. 6281234567890)"></textarea>
    <div class="note">Example: 6281234567890<br>Jika WhatsApp Web belum login, silakan login terlebih dahulu di tab yang terbuka.</div>

    <label for="message">Message</label>
    <textarea id="message" placeholder="Type your message here..."></textarea>

    <button id="sendBtn" aria-label="Send WhatsApp messages to contacts">Send Messages</button>
    <div id="feedback" role="alert" aria-live="polite"></div>
    <div class="footer">Open WhatsApp Web tabs to send your messages.<br>Jika pesan tidak terkirim otomatis, klik tombol kirim di WhatsApp Web secara manual.</div>
  </div>

  <script>
    (function() {
      const sendBtn = document.getElementById('sendBtn');
      const contactsInput = document.getElementById('contacts');
      const messageInput = document.getElementById('message');
      const feedback = document.getElementById('feedback');

      function sanitizeNumber(number) {
        // Remove spaces, dashes, plus signs (We expect international format without plus)
        return number.replace(/[\s\-\+]/g, '');
      }

      function isValidNumber(number) {
        // Simple validation: only digits, length between 8 and 15 (typical international lengths)
        return /^[0-9]{8,15}$/.test(number);
      }

      sendBtn.addEventListener('click', () => {
        feedback.textContent = '';
        feedback.className = '';

        const rawContacts = contactsInput.value.trim();
        const message = messageInput.value.trim();

        if (!rawContacts) {
          feedback.textContent = 'Please enter at least one phone number.';
          feedback.className = 'error';
          return;
        }

        if (!message) {
          feedback.textContent = 'Please enter a message to send.';
          feedback.className = 'error';
          return;
        }

        const contacts = rawContacts
          .split('\n')
          .map(sanitizeNumber)
          .filter(c => c.length > 0);

        if (contacts.length === 0) {
          feedback.textContent = 'No valid phone numbers found.';
          feedback.className = 'error';
          return;
        }

        const invalidNumbers = contacts.filter(n => !isValidNumber(n));
        if (invalidNumbers.length > 0) {
          feedback.textContent = `Invalid phone numbers detected: ${invalidNumbers.join(', ')}`;
          feedback.className = 'error';
          return;
        }

        // Jika lebih dari 1 kontak, beri instruksi manual agar tidak diblokir popup
        if (contacts.length > 1) {
          feedback.innerHTML = `Akan dibuka tab untuk kontak pertama saja. Untuk kontak berikutnya, copy-paste link berikut ke browser secara manual:<br><br>`;
          const encodedMsg = encodeURIComponent(message);
          const links = contacts.map(number => `<a href="https://wa.me/${number}?text=${encodedMsg}" target="_blank">${number}</a>`);
          feedback.innerHTML += links.join('<br>');
          feedback.className = 'success';
          // Buka tab pertama saja
          window.open(`https://wa.me/${contacts[0]}?text=${encodedMsg}`, '_blank', 'noopener');
        } else {
          // Satu kontak, langsung buka
          const encodedMsg = encodeURIComponent(message);
          window.open(`https://wa.me/${contacts[0]}?text=${encodedMsg}`, '_blank', 'noopener');
          feedback.textContent = `Tab WhatsApp Web sudah dibuka untuk ${contacts[0]}. Jika belum login, silakan login dulu.`;
          feedback.className = 'success';
        }
      });
    })();
  </script>

</body>
</html>

